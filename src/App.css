/* CSS Variables for Theme */
:root {
  --primary-color: #6366f1;
  --primary-hover: #5855eb;
  --secondary-color: #f59e0b;
  --accent-color: #10b981;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

.app.dark {
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --border-color: #374151;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app {
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Theme Toggle */
.theme-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  margin-bottom: 0.75rem;
}

p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Buttons */
.cta-button,
.submit-button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.cta-button:hover,
.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.button-arrow {
  transition: transform 0.3s ease;
}

.cta-button:hover .button-arrow,
.submit-button:hover .button-arrow {
  transform: translateX(4px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Utility Classes */
.highlight {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.hero.visible .hero-content {
  opacity: 1;
  transform: translateY(0);
}

.hero-headline {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.hero-subheadline {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  width: 280px;
  height: 560px;
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  border-radius: 2.5rem;
  padding: 1rem;
  box-shadow: var(--shadow-xl);
  position: relative;
  animation: pulse 4s ease-in-out infinite;
}

.app.dark .phone-mockup {
  background: linear-gradient(145deg, #374151, #4b5563);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: var(--bg-secondary);
  border-radius: 2rem;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: hidden;
}

.app-interface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
}

.notification {
  background: var(--gradient-primary);
  color: white;
  padding: 1rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  animation: fadeInUp 1s ease 0.5s both;
}

.notification-icon {
  font-size: 1.25rem;
}

.progress-ring {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 2rem auto;
  border-radius: 50%;
  background: conic-gradient(var(--primary-color) 0deg 270deg, var(--border-color) 270deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 1s ease 1s both;
}

.progress-ring::before {
  content: '';
  position: absolute;
  width: 80px;
  height: 80px;
  background: var(--bg-secondary);
  border-radius: 50%;
}

.progress-text {
  position: relative;
  z-index: 1;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .theme-toggle {
    top: 1rem;
    right: 1rem;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .phone-mockup {
    width: 240px;
    height: 480px;
  }
}

/* Features Section */
.features {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.features-title {
  text-align: center;
  margin-bottom: 4rem;
  color: var(--text-primary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 6rem;
}

.feature-card {
  background: var(--bg-primary);
  padding: 2.5rem;
  border-radius: 1.5rem;
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(30px);
  border: 1px solid var(--border-color);
}

.feature-card.visible {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.6s ease forwards;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.feature-title {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

.features-cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 3rem;
  background: var(--bg-primary);
  border-radius: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.cta-title {
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-size: clamp(1.5rem, 3vw, 2.5rem);
}

.cta-quote {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  font-style: italic;
  color: var(--text-secondary);
  margin: 2rem 0;
  padding: 1.5rem;
  border-left: 4px solid var(--primary-color);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
}

.cta-description {
  color: var(--text-secondary);
  font-size: 1.125rem;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .features {
    padding: 4rem 0;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 2rem;
  }

  .features-cta {
    padding: 2rem;
    margin: 0 1rem;
  }
}

/* Survey Section */
.survey {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.survey-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.survey-header {
  text-align: center;
  margin-bottom: 3rem;
}

.survey-header h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.survey-header p {
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.survey-form {
  background: var(--bg-secondary);
  padding: 3rem;
  border-radius: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s ease;
}

.radio-label:hover {
  background: var(--bg-tertiary);
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.radio-label input[type="radio"]:checked+.radio-custom {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.radio-label input[type="radio"]:checked+.radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
}

.payment-input {
  position: relative;
  display: flex;
  align-items: center;
}

.currency {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
  font-weight: 600;
  z-index: 1;
}

.payment-input input {
  padding-left: 2.5rem;
}

.submit-button {
  width: 100%;
  margin-top: 1rem;
  justify-content: center;
}

/* Thank You Section */
.thank-you {
  text-align: center;
  padding: 4rem 2rem;
}

.thank-you h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.thank-you p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.success-animation {
  margin: 2rem 0;
}

.checkmark {
  display: inline-block;
  width: 4rem;
  height: 4rem;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  animation: pulse 2s ease-in-out infinite;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .survey {
    padding: 4rem 0;
  }

  .survey-form {
    padding: 2rem;
  }

  .radio-group {
    gap: 0.5rem;
  }
}

/* Footer Section */
.footer {
  background: var(--bg-secondary);
  padding: 3rem 0;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-tagline {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.footer-copyright {
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Additional Animations */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .theme-toggle {
    display: none;
  }

  .hero-visual {
    display: none;
  }

  .app {
    background: white !important;
    color: black !important;
  }
}