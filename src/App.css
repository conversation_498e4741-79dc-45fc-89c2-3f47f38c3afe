/* CSS Variables for Theme */
:root {
  --primary-color: #6366f1;
  --primary-hover: #5855eb;
  --secondary-color: #f59e0b;
  --accent-color: #10b981;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

.app.dark {
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --border-color: #374151;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app {
  width: 100%;
  min-height: 100vh;
  transition: all 0.3s ease;
  position: relative;
}

/* Theme Toggle */
.theme-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  margin-bottom: 0.75rem;
}

p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Buttons */
.cta-button,
.submit-button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.cta-button:hover,
.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.button-arrow {
  transition: transform 0.3s ease;
}

.cta-button:hover .button-arrow,
.submit-button:hover .button-arrow {
  transform: translateX(4px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Three.js Background */
.three-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

/* Utility Classes */
.highlight {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.hero {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.hero-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 3rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.hero.visible .hero-content {
  opacity: 1;
  transform: translateY(0);
}

.hero-headline {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.hero-subheadline {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  width: 280px;
  height: 560px;
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  border-radius: 2.5rem;
  padding: 1rem;
  box-shadow: var(--shadow-xl);
  position: relative;
  animation: pulse 4s ease-in-out infinite;
}

.app.dark .phone-mockup {
  background: linear-gradient(145deg, #374151, #4b5563);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: var(--bg-secondary);
  border-radius: 2rem;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: hidden;
}

.app-interface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
}

.notification {
  background: var(--gradient-primary);
  color: white;
  padding: 1rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  animation: fadeInUp 1s ease 0.5s both;
}

.notification-icon {
  font-size: 1.25rem;
}

.progress-ring {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 2rem auto;
  border-radius: 50%;
  background: conic-gradient(var(--primary-color) 0deg 270deg, var(--border-color) 270deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 1s ease 1s both;
}

.progress-ring::before {
  content: '';
  position: absolute;
  width: 80px;
  height: 80px;
  background: var(--bg-secondary);
  border-radius: 50%;
}

.progress-text {
  position: relative;
  z-index: 1;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .theme-toggle {
    top: 1rem;
    right: 1rem;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .phone-mockup {
    width: 240px;
    height: 480px;
  }
}

/* Features Section */
.features {
  width: 100%;
  padding: 8rem 0;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  position: relative;
}

.app.dark .features {
  background: rgba(0, 0, 0, 0.1);
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(245, 158, 11, 0.03) 100%);
  pointer-events: none;
}

.features-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 3rem;
  position: relative;
  z-index: 1;
}

.features-title {
  text-align: center;
  margin-bottom: 5rem;
  color: var(--text-primary);
  position: relative;
}

.features-title::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-bottom: 8rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 2rem;
  box-shadow: var(--shadow-lg);
  text-align: center;
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.app.dark .feature-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card.visible {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.6s ease forwards;
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  font-size: 4rem;
  margin-bottom: 2rem;
  display: block;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
}

.feature-title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.1rem;
}

.features-cta {
  text-align: center;
  max-width: 1000px;
  margin: 0 auto;
  padding: 4rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 3rem;
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.app.dark .features-cta {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.features-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.05;
  pointer-events: none;
}

.cta-title {
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-size: clamp(1.8rem, 4vw, 3rem);
  font-weight: 800;
  position: relative;
  z-index: 1;
}

.cta-quote {
  font-size: clamp(1.25rem, 2.5vw, 1.8rem);
  font-style: italic;
  color: var(--text-secondary);
  margin: 3rem 0;
  padding: 2rem;
  border-left: 6px solid var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
  border-radius: 1rem;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.cta-description {
  color: var(--text-secondary);
  font-size: 1.25rem;
  line-height: 1.7;
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .features {
    padding: 6rem 0;
  }

  .features-container {
    padding: 0 1.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .feature-card {
    padding: 2.5rem 2rem;
  }

  .features-cta {
    padding: 3rem 2rem;
    margin: 0 1rem;
  }

  .hero-content {
    padding: 0 1.5rem;
    gap: 3rem;
  }
}

/* Survey Section */
.survey {
  width: 100%;
  padding: 8rem 0;
  background: transparent;
  position: relative;
}

.survey::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.03) 0%, rgba(99, 102, 241, 0.03) 100%);
  pointer-events: none;
}

.survey-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 3rem;
  position: relative;
  z-index: 1;
}

.survey-header {
  text-align: center;
  margin-bottom: 4rem;
}

.survey-header h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
}

.survey-header p {
  color: var(--text-secondary);
  font-size: 1.25rem;
  line-height: 1.6;
}

.survey-form {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  padding: 4rem;
  border-radius: 3rem;
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.app.dark .survey-form {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.survey-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-accent);
  opacity: 0.03;
  pointer-events: none;
}

.form-group {
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1.25rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  color: var(--text-primary);
  font-size: 1.1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.app.dark .form-group input,
.app.dark .form-group select,
.app.dark .form-group textarea {
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.05);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.15);
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.radio-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.radio-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 1rem 1.5rem;
  border-radius: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  text-align: center;
}

.app.dark .radio-label {
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.05);
}

.radio-label:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 1.5rem;
  height: 1.5rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.radio-label input[type="radio"]:checked+.radio-custom {
  border-color: var(--primary-color);
  background: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

.radio-label input[type="radio"]:checked+.radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.6rem;
  height: 0.6rem;
  background: white;
  border-radius: 50%;
}

.radio-label input[type="radio"]:checked {
  &+.radio-custom+span {
    color: var(--primary-color);
  }
}

.payment-input {
  position: relative;
  display: flex;
  align-items: center;
}

.currency {
  position: absolute;
  left: 1.5rem;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.2rem;
  z-index: 2;
}

.payment-input input {
  padding-left: 3rem;
}

.submit-button {
  width: 100%;
  margin-top: 2rem;
  justify-content: center;
  padding: 1.5rem 2rem;
  font-size: 1.25rem;
  font-weight: 700;
  border-radius: 1.5rem;
  background: var(--gradient-primary);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
}

/* Thank You Section */
.thank-you {
  text-align: center;
  padding: 4rem 2rem;
}

.thank-you h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.thank-you p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.success-animation {
  margin: 2rem 0;
}

.checkmark {
  display: inline-block;
  width: 4rem;
  height: 4rem;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  animation: pulse 2s ease-in-out infinite;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .survey {
    padding: 6rem 0;
  }

  .survey-container {
    padding: 0 1.5rem;
  }

  .survey-form {
    padding: 3rem 2rem;
  }

  .radio-group {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .radio-label {
    padding: 1rem;
    justify-content: flex-start;
  }
}

/* Footer Section */
.footer {
  background: var(--bg-secondary);
  padding: 3rem 0;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-tagline {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.footer-copyright {
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Additional Animations */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .theme-toggle {
    display: none;
  }

  .hero-visual {
    display: none;
  }

  .app {
    background: white !important;
    color: black !important;
  }
}