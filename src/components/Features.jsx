import { useState, useEffect, useRef } from 'react'

const Features = () => {
  const [visibleFeatures, setVisibleFeatures] = useState(new Set())
  const featuresRef = useRef([])

  const features = [
    {
      id: 'voice-onboarding',
      icon: '🎤',
      title: 'AI-Driven Voice Onboarding',
      description: 'A friendly voice agent asks about your goals, motivations, and struggles to build a plan around you.'
    },
    {
      id: 'geolocation',
      icon: '📍',
      title: 'Geolocation-Based Accountability',
      description: 'We know when you\'re near the gym. If you\'re slacking — the app knows. If you\'re consistent — it celebrates.'
    },
    {
      id: 'motivation',
      icon: '💪',
      title: 'Motivational Engine',
      description: 'Your "why" drives your nudges. Whether it\'s confidence, discipline, or revenge on your ex — we\'ll remind you at the right time.'
    },
    {
      id: 'contracts',
      icon: '💰',
      title: 'Commitment Contracts',
      description: 'Put money on the line. Skip your workout? Your money goes to a pool that rewards your future progress — no hard punishment, just smart psychology.'
    },
    {
      id: 'ai-growth',
      icon: '🧠',
      title: 'AI That Grows With You',
      description: 'It notices your excuses. It spots plateaus. And it adjusts its tone, intensity, and rest advice accordingly.'
    }
  ]

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const featureId = entry.target.dataset.featureId
            setVisibleFeatures(prev => new Set([...prev, featureId]))
          }
        })
      },
      { threshold: 0.3 }
    )

    featuresRef.current.forEach((ref) => {
      if (ref) observer.observe(ref)
    })

    return () => observer.disconnect()
  }, [])

  return (
    <section className="features">
      <div className="features-container">
        <h2 className="features-title">Core Features</h2>
        
        <div className="features-grid">
          {features.map((feature, index) => (
            <div
              key={feature.id}
              ref={el => featuresRef.current[index] = el}
              data-feature-id={feature.id}
              className={`feature-card ${visibleFeatures.has(feature.id) ? 'visible' : ''}`}
            >
              <div className="feature-icon">{feature.icon}</div>
              <h3 className="feature-title">{feature.title}</h3>
              <p className="feature-description">{feature.description}</p>
            </div>
          ))}
        </div>
        
        <div className="features-cta">
          <h3 className="cta-title">We're Not Building Just Another Fitness App.</h3>
          <blockquote className="cta-quote">
            "Everybody wants to build a 100-billion-dollar company. How can you do that with a subpar body?"
          </blockquote>
          <p className="cta-description">
            Your health is the foundation of everything else. Let's build it the smart, consistent, no-BS way.
          </p>
        </div>
      </div>
    </section>
  )
}

export default Features
