import { useState, useEffect } from 'react'

const Hero = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const scrollToSurvey = () => {
    const surveySection = document.getElementById('survey')
    if (surveySection) {
      surveySection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className={`hero ${isVisible ? 'visible' : ''}`}>
      <div className="hero-content">
        <h1 className="hero-headline">
          Bet on Yourself. <span className="highlight">Literally.</span>
        </h1>
        
        <p className="hero-subheadline">
          An AI-powered accountability partner that ensures you actually show up to the gym — not just talk about it.
        </p>
        
        <button 
          className="cta-button"
          onClick={scrollToSurvey}
        >
          Take the Survey
          <span className="button-arrow">→</span>
        </button>
        
        <div className="hero-visual">
          <div className="phone-mockup">
            <div className="phone-screen">
              <div className="app-interface">
                <div className="notification">
                  <span className="notification-icon">🎯</span>
                  <span>Time to hit the gym! Your future self is counting on you.</span>
                </div>
                <div className="progress-ring">
                  <div className="progress-fill"></div>
                  <span className="progress-text">3/4 workouts this week</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
