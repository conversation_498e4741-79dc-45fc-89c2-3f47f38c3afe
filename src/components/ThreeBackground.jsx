import { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Points, PointMaterial } from '@react-three/drei'
import * as random from 'maath/random/dist/maath-random.esm'

function Stars(props) {
  const ref = useRef()
  const [sphere] = useMemo(() => [random.inSphere(new Float32Array(5000), { radius: 1.5 })], [])

  useFrame((state, delta) => {
    ref.current.rotation.x -= delta / 10
    ref.current.rotation.y -= delta / 15
  })

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={ref} positions={sphere} stride={3} frustumCulled={false} {...props}>
        <PointMaterial
          transparent
          color="#6366f1"
          size={0.005}
          sizeAttenuation={true}
          depthWrite={false}
        />
      </Points>
    </group>
  )
}

function FloatingOrbs() {
  const orbsRef = useRef()
  
  useFrame((state) => {
    if (orbsRef.current) {
      orbsRef.current.rotation.y = state.clock.elapsedTime * 0.1
      orbsRef.current.children.forEach((orb, i) => {
        orb.position.y = Math.sin(state.clock.elapsedTime + i) * 0.5
        orb.rotation.x = state.clock.elapsedTime * 0.5
        orb.rotation.z = state.clock.elapsedTime * 0.3
      })
    }
  })

  return (
    <group ref={orbsRef}>
      {Array.from({ length: 8 }, (_, i) => (
        <mesh
          key={i}
          position={[
            Math.cos((i / 8) * Math.PI * 2) * 3,
            0,
            Math.sin((i / 8) * Math.PI * 2) * 3
          ]}
        >
          <sphereGeometry args={[0.1, 16, 16]} />
          <meshBasicMaterial
            color={i % 2 === 0 ? "#6366f1" : "#f59e0b"}
            transparent
            opacity={0.6}
          />
        </mesh>
      ))}
    </group>
  )
}

function WaveGrid() {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.1
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.5
    }
  })

  return (
    <mesh ref={meshRef} position={[0, -2, -5]} rotation={[-Math.PI / 3, 0, 0]}>
      <planeGeometry args={[20, 20, 50, 50]} />
      <meshBasicMaterial
        color="#6366f1"
        wireframe
        transparent
        opacity={0.1}
      />
    </mesh>
  )
}

const ThreeBackground = ({ darkMode }) => {
  return (
    <div className="three-background">
      <Canvas
        camera={{ position: [0, 0, 1] }}
        style={{ 
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: -1,
          background: darkMode 
            ? 'linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f9fafb 50%, #f3f4f6 100%)'
        }}
      >
        <Stars />
        <FloatingOrbs />
        <WaveGrid />
      </Canvas>
    </div>
  )
}

export default ThreeBackground
