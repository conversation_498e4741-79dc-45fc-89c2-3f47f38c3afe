import { useState } from 'react'

const Survey = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    intendedWorkouts: '',
    actualWorkouts: '',
    skipReason: '',
    consistency: '',
    betMoney: '',
    betaTest: '',
    monthlyPayment: '',
    additionalFeatures: ''
  })
  
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [errors, setErrors] = useState({})

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.name.trim()) newErrors.name = 'Name is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    if (!formData.email.includes('@')) newErrors.email = 'Please enter a valid email'
    if (!formData.intendedWorkouts) newErrors.intendedWorkouts = 'Please answer this question'
    if (!formData.actualWorkouts) newErrors.actualWorkouts = 'Please answer this question'
    if (!formData.skipReason.trim()) newErrors.skipReason = 'Please answer this question'
    if (!formData.consistency) newErrors.consistency = 'Please select an option'
    if (!formData.betMoney) newErrors.betMoney = 'Please select an option'
    if (!formData.betaTest) newErrors.betaTest = 'Please select an option'
    if (!formData.monthlyPayment.trim()) newErrors.monthlyPayment = 'Please enter an amount'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (validateForm()) {
      // Here you would typically send the data to your backend
      console.log('Form submitted:', formData)
      setIsSubmitted(true)
    }
  }

  if (isSubmitted) {
    return (
      <section id="survey" className="survey">
        <div className="survey-container">
          <div className="thank-you">
            <h2>Thank You! 🎉</h2>
            <p>Every answer helps shape this project. We're building this for people like you — and with you.</p>
            <div className="success-animation">
              <div className="checkmark">✓</div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section id="survey" className="survey">
      <div className="survey-container">
        <div className="survey-header">
          <h2>Help Us Build the App You Want</h2>
          <p>Answer 8 quick questions and get early access when we launch. <strong>Free for first 500 users.</strong></p>
        </div>
        
        <form className="survey-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="name">Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={errors.name ? 'error' : ''}
            />
            {errors.name && <span className="error-message">{errors.name}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="email">Email *</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={errors.email ? 'error' : ''}
            />
            {errors.email && <span className="error-message">{errors.email}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="intendedWorkouts">1. How often do you intend to work out each week? *</label>
            <select
              id="intendedWorkouts"
              name="intendedWorkouts"
              value={formData.intendedWorkouts}
              onChange={handleInputChange}
              className={errors.intendedWorkouts ? 'error' : ''}
            >
              <option value="">Select frequency</option>
              <option value="1-2">1-2 times</option>
              <option value="3-4">3-4 times</option>
              <option value="5-6">5-6 times</option>
              <option value="7+">7+ times</option>
            </select>
            {errors.intendedWorkouts && <span className="error-message">{errors.intendedWorkouts}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="actualWorkouts">2. How often do you actually work out each week? *</label>
            <select
              id="actualWorkouts"
              name="actualWorkouts"
              value={formData.actualWorkouts}
              onChange={handleInputChange}
              className={errors.actualWorkouts ? 'error' : ''}
            >
              <option value="">Select frequency</option>
              <option value="0">0 times</option>
              <option value="1-2">1-2 times</option>
              <option value="3-4">3-4 times</option>
              <option value="5-6">5-6 times</option>
              <option value="7+">7+ times</option>
            </select>
            {errors.actualWorkouts && <span className="error-message">{errors.actualWorkouts}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="skipReason">3. What's the #1 reason you skip workouts? *</label>
            <textarea
              id="skipReason"
              name="skipReason"
              value={formData.skipReason}
              onChange={handleInputChange}
              placeholder="Be honest - we're here to help solve this!"
              className={errors.skipReason ? 'error' : ''}
            />
            {errors.skipReason && <span className="error-message">{errors.skipReason}</span>}
          </div>

          <div className="form-group">
            <label>4. Do you struggle with staying consistent at the gym? *</label>
            <div className="radio-group">
              {['Yes', 'No', 'Sometimes'].map(option => (
                <label key={option} className="radio-label">
                  <input
                    type="radio"
                    name="consistency"
                    value={option}
                    checked={formData.consistency === option}
                    onChange={handleInputChange}
                  />
                  <span className="radio-custom"></span>
                  {option}
                </label>
              ))}
            </div>
            {errors.consistency && <span className="error-message">{errors.consistency}</span>}
          </div>

          <div className="form-group">
            <label>5. Would you ever bet money to ensure you went to the gym? *</label>
            <div className="radio-group">
              {['Yes', 'No', 'Maybe'].map(option => (
                <label key={option} className="radio-label">
                  <input
                    type="radio"
                    name="betMoney"
                    value={option}
                    checked={formData.betMoney === option}
                    onChange={handleInputChange}
                  />
                  <span className="radio-custom"></span>
                  {option}
                </label>
              ))}
            </div>
            {errors.betMoney && <span className="error-message">{errors.betMoney}</span>}
          </div>

          <div className="form-group">
            <label>6. If we launch a private beta, would you like to test it? *</label>
            <div className="radio-group">
              {['Yes', 'No', 'Maybe'].map(option => (
                <label key={option} className="radio-label">
                  <input
                    type="radio"
                    name="betaTest"
                    value={option}
                    checked={formData.betaTest === option}
                    onChange={handleInputChange}
                  />
                  <span className="radio-custom"></span>
                  {option}
                </label>
              ))}
            </div>
            {errors.betaTest && <span className="error-message">{errors.betaTest}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="monthlyPayment">7. How much would you consider paying per month? *</label>
            <div className="payment-input">
              <span className="currency">$</span>
              <input
                type="number"
                id="monthlyPayment"
                name="monthlyPayment"
                value={formData.monthlyPayment}
                onChange={handleInputChange}
                placeholder="0"
                min="0"
                step="0.01"
                className={errors.monthlyPayment ? 'error' : ''}
              />
            </div>
            {errors.monthlyPayment && <span className="error-message">{errors.monthlyPayment}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="additionalFeatures">8. Anything else you'd love to see in a gym accountability partner?</label>
            <textarea
              id="additionalFeatures"
              name="additionalFeatures"
              value={formData.additionalFeatures}
              onChange={handleInputChange}
              placeholder="Share your ideas - we're listening!"
            />
          </div>

          <button type="submit" className="submit-button">
            Submit Survey
            <span className="button-arrow">→</span>
          </button>
        </form>
      </div>
    </section>
  )
}

export default Survey
