import { useState } from 'react'
import './App.css'
import ThreeBackground from './components/ThreeBackground'
import Hero from './components/Hero'
import Features from './components/Features'
import Survey from './components/Survey'
import Footer from './components/Footer'

function App() {
  const [darkMode, setDarkMode] = useState(false)

  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
  }

  return (
    <div className={`app ${darkMode ? 'dark' : 'light'}`}>
      <ThreeBackground darkMode={darkMode} />

      <button
        className="theme-toggle"
        onClick={toggleDarkMode}
        aria-label="Toggle dark mode"
      >
        {darkMode ? '☀️' : '🌙'}
      </button>

      <Hero />
      <Features />
      <Survey />
      <Footer />
    </div>
  )
}

export default App
