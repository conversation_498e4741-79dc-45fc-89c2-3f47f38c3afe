import { useState, useEffect, createContext, useContext, useRef } from 'react'
import emailjs from '@emailjs/browser'
import './App.css'

// Custom hook for intersection observer
function useIntersectionObserver(options = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, {
      threshold: 0.1,
      ...options
    })

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [])

  return [ref, isIntersecting]
}

// Theme Context
const ThemeContext = createContext()

function App() {
  const [theme, setTheme] = useState('dark')
  const [formData, setFormData] = useState({
    name: '',
    contact: '',
    goal: '',
    struggle: '',
    workoutType: '',
    stakeMoney: '',
    pricing: ''
  })

  const toggleTheme = () => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark')
  }

  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme)
  }, [theme])

  const scrollToForm = () => {
    document.getElementById('survey-form').scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      <div className="app">
        <Header />
        <HeroSection scrollToForm={scrollToForm} />
        <ProblemSection />
        <DifferenceSection />
        <ScenariosSection />
        <VisionSection />
        <SurveySection formData={formData} setFormData={setFormData} />
        <Footer />
      </div>
    </ThemeContext.Provider>
  )
}

// Header Component
function Header() {
  const { theme, toggleTheme } = useContext(ThemeContext)

  return (
    <header className="header">
      <div className="container">
        <div className="logo">
          <span className="logo-text">💪 Qare AI</span>
        </div>
        <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
          {theme === 'dark' ? '☀️' : '🌙'}
        </button>
      </div>
    </header>
  )
}

// Hero Section
function HeroSection({ scrollToForm }) {
  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          <h1 className="hero-title">
            Don't Have a Personal Trainer?<br />
            <span className="highlight">Now You Do.</span>
          </h1>
          <p className="hero-subtitle">
            An AI-powered accountability coach that knows when you skip the gym — and makes sure you show up, stay consistent, and crush your goals.
          </p>
          <div className="hero-buttons">
            <button className="btn btn-secondary" onClick={scrollToForm}>
              📝 Take 1-Minute Survey
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

// Problem Section
function ProblemSection() {
  return (
    <section className="problem">
      <div className="container">
        <h2 className="section-title">Fitness Apps Track — But They Don't Care</h2>
        <div className="problem-list">
          <div className="problem-item">
            <span className="problem-icon">😐</span>
            <p>They let you "log" workouts — but never follow up.</p>
          </div>
          <div className="problem-item">
            <span className="problem-icon">📱</span>
            <p>They track steps — but don't know when you're plateauing.</p>
          </div>
          <div className="problem-item">
            <span className="problem-icon">❌</span>
            <p>They don't tell you what you're missing or why it matters.</p>
          </div>
        </div>
        <p className="problem-tagline">We're building an AI that actually gives a damn.</p>
      </div>
    </section>
  )
}

// Difference Section
function DifferenceSection() {
  const [sectionRef, isVisible] = useIntersectionObserver({ threshold: 0.2 })

  return (
    <section ref={sectionRef} className="difference">
      <div className="container">
        <h2 className="section-title">Meet the Fitness App That Calls You Out (Lovingly)</h2>
        <div className={`difference-grid ${isVisible ? 'animate-in' : ''}`}>
          <div className="difference-card" style={{ animationDelay: '0.1s' }}>
            <div className="card-icon">🧠</div>
            <h3>AI with Personality</h3>
            <p>Tailored voice-based onboarding.</p>
            <p>Becomes your coach, cheerleader, or drill sergeant.</p>
          </div>
          <div className="difference-card" style={{ animationDelay: '0.2s' }}>
            <div className="card-icon">📍</div>
            <h3>Gym-Aware Nudges</h3>
            <p>Knows when you're at (or near) the gym.</p>
            <p>Celebrates consistency, calls out ghosting.</p>
          </div>
          <div className="difference-card" style={{ animationDelay: '0.3s' }}>
            <div className="card-icon">🔄</div>
            <h3>Real Feedback</h3>
            <p>Real advice on form, recovery, nutrition.</p>
            <p>Plateaus? Overtraining? It tells you.</p>
          </div>
        </div>
      </div>
    </section>
  )
}

// Scenarios Section
function ScenariosSection() {
  const [sectionRef, isVisible] = useIntersectionObserver({ threshold: 0.2 })

  const scenarios = [
    {
      icon: "💬",
      text: "Hey, you've hit chest 3 days straight. Let's give those fibers some rest."
    },
    {
      icon: "🏋️",
      text: "Side delts missing? Add 4 sets of lateral raises today."
    },
    {
      icon: "🧪",
      text: "Your BCA results show low hydration. Electrolytes up."
    },
    {
      icon: "💰",
      text: "Missed a workout? Your staked money bought you protein bars."
    }
  ]

  return (
    <section ref={sectionRef} className="scenarios">
      <div className="container">
        <h2 className="section-title">Imagine This...</h2>
        <div className={`scenarios-grid ${isVisible ? 'animate-in' : ''}`}>
          {scenarios.map((scenario, index) => (
            <div
              key={index}
              className="scenario-card"
              style={{ animationDelay: `${(index + 1) * 0.1}s` }}
            >
              <div className="scenario-icon">{scenario.icon}</div>
              <p className="scenario-text">"{scenario.text}"</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Vision Section
function VisionSection() {
  return (
    <section className="vision">
      <div className="container">
        <h2 className="section-title">We're Not Building Just Another Fitness App.</h2>
        <div className="vision-quote">
          <blockquote>
            "Everybody wants to build a 100-billion-dollar company. How can you do that with a subpar body?"
          </blockquote>
          <p className="vision-subtext">
            Your health is the foundation of everything else. Let's build it the smart, consistent, no-BS way.
          </p>
        </div>
      </div>
    </section>
  )
}

// Survey Section
function SurveySection({ formData, setFormData }) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState({})

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }



  const validateForm = () => {
    const newErrors = {}
    if (!formData.name.trim()) newErrors.name = 'Name is required'
    if (!formData.contact.trim()) newErrors.contact = 'Contact is required'
    if (!formData.goal) newErrors.goal = 'Please select a goal'
    if (!formData.struggle.trim()) newErrors.struggle = 'Please describe your struggle'
    if (!formData.workoutType) newErrors.workoutType = 'Please select workout type'
    if (!formData.stakeMoney) newErrors.stakeMoney = 'Please select an option'
    if (!formData.pricing) newErrors.pricing = 'Please select a pricing option'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!validateForm()) return

    setIsSubmitting(true)
    try {
      // EmailJS config
      const serviceId = 'service_96zpdkk'
      const templateId = 'template_fva6ntr' // TODO: Replace with your actual template ID
      const publicKey = 'P_dVcIX_qmw_RJT8q' // TODO: Replace with your actual public key from EmailJS dashboard

      // Prepare template params
      const templateParams = {
        name: formData.name,
        contact: formData.contact,
        goal: formData.goal,
        struggle: formData.struggle,
        workoutType: formData.workoutType,
        stakeMoney: formData.stakeMoney,
        pricing: formData.pricing
      }

      await emailjs.send(serviceId, templateId, templateParams, publicKey)
      alert("Thank you! You'll be among the first to know when we launch.")
    } catch (error) {
      alert('There was an error submitting the form. Please try again later.')
      console.error(error)
    }
    setIsSubmitting(false)
  }

  return (
    <section id="survey-form" className="survey">
      <div className="container">
        <h2 className="section-title">Help Us Build the App You Want</h2>
        <p className="survey-subtitle">
          Answer 5 quick questions and get early access when we launch. Free for first 500 users.
        </p>

        <form className="survey-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={errors.name ? 'error' : ''}
              placeholder="Your name"
            />
            {errors.name && <span className="error-text">{errors.name}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="contact">Email or WhatsApp</label>
            <input
              type="text"
              id="contact"
              value={formData.contact}
              onChange={(e) => handleInputChange('contact', e.target.value)}
              className={errors.contact ? 'error' : ''}
              placeholder="<EMAIL> or +1234567890"
            />
            {errors.contact && <span className="error-text">{errors.contact}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="goal">What's your fitness goal?</label>
            <select
              id="goal"
              value={formData.goal}
              onChange={(e) => handleInputChange('goal', e.target.value)}
              className={errors.goal ? 'error' : ''}
            >
              <option value="">Select your goal</option>
              <option value="recomp">Recomp</option>
              <option value="cutting">Cutting</option>
              <option value="bulking">Bulking</option>
            </select>
            {errors.goal && <span className="error-text">{errors.goal}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="struggle">What's your biggest struggle in staying consistent?</label>
            <textarea
              id="struggle"
              value={formData.struggle}
              onChange={(e) => handleInputChange('struggle', e.target.value)}
              className={errors.struggle ? 'error' : ''}
              placeholder="Tell us what makes it hard to stick to your routine..."
              rows="3"
            />
            {errors.struggle && <span className="error-text">{errors.struggle}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="workoutType">Do you use the gym, home workouts, or both?</label>
            <select
              id="workoutType"
              value={formData.workoutType}
              onChange={(e) => handleInputChange('workoutType', e.target.value)}
              className={errors.workoutType ? 'error' : ''}
            >
              <option value="">Select workout type</option>
              <option value="gym">Gym</option>
              <option value="home">Home workouts</option>
              <option value="both">Both</option>
            </select>
            {errors.workoutType && <span className="error-text">{errors.workoutType}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="stakeMoney">Would you be open to staking money for commitment?</label>
            <select
              id="stakeMoney"
              value={formData.stakeMoney}
              onChange={(e) => handleInputChange('stakeMoney', e.target.value)}
              className={errors.stakeMoney ? 'error' : ''}
            >
              <option value="">Select an option</option>
              <option value="yes">Yes, I'm interested</option>
              <option value="maybe">Maybe, tell me more</option>
              <option value="no">No, not for me</option>
            </select>
            {errors.stakeMoney && <span className="error-text">{errors.stakeMoney}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="pricing">How much would you pay for this?</label>
            <select
              id="pricing"
              value={formData.pricing}
              onChange={(e) => handleInputChange('pricing', e.target.value)}
              className={errors.pricing ? 'error' : ''}
            >
              <option value="">Select a price range</option>
              <option value="free">Free (with ads)</option>
              <option value="5-10">$5-10/month</option>
              <option value="10-20">$10-20/month</option>
              <option value="20-50">$20-50/month</option>
              <option value="50+">$50+/month</option>
            </select>
            {errors.pricing && <span className="error-text">{errors.pricing}</span>}
          </div>

          <button type="submit" className="btn btn-primary submit-btn" disabled={isSubmitting}>
            {isSubmitting ? '💪 Submitting...' : '💪 I Want This – Count Me In'}
          </button>
        </form>
      </div>
    </section>
  )
}

// Footer
function Footer() {
  return (
    <footer className="footer">
      <div className="container">
        <h3 className="footer-tagline">
          The Future of Fitness Is Here — And It's Watching You 😏
        </h3>
        <p className="footer-credits">
          Built with love, sweat, and ChatGPT prompts 💥
        </p>
        <p className="footer-copyright">
          © 2025 Qare AI Labs
        </p>
      </div>
    </footer>
  )
}

export default App
